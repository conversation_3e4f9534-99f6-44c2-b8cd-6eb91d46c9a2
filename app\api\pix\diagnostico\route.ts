import { NextRequest, NextResponse } from "next/server"

const PIX_API_TOKEN = process.env.PIX_API_TOKEN || 'Gk0e0W5bc2Guozw97NXv8duZbZCDbsedwMuTQNqqn5ZMT549Z0qDDZBF5ZbG9sHhXw9egbly3eiFJ4PKfs6ur+zzXs30gSDV6CYl8sQmSPJaISqpqf/FtJ2k30O3kO9ZsNwasalD0PGpD2wlAbb4ynO5S07P1kgWZRtw4w=='

export async function GET(request: NextRequest) {
  try {
    const diagnostico = {
      timestamp: new Date().toISOString(),
      configuracao: {
        PIX_API_URL: process.env.PIX_API_URL || 'NÃO CONFIGURADO',
        PIX_API_TOKEN_CONFIGURADO: !!process.env.PIX_API_TOKEN,
        PIX_WEBHOOK_URL: process.env.PIX_WEBHOOK_URL || 'NÃO CONFIGURADO',
        NODE_ENV: process.env.NODE_ENV || 'development'
      },
      urls_testadas: [],
      status_apis: {}
    }

    // Testar diferentes URLs da API PIX
    const urlsParaTestar = [
      'https://ouroemu.site/api/v1/MP/webhookruntransation?order_id=TEST123',
      'https://api.meiodepagamento.com/api/V1/ConsultarTransacao',
      'https://ouroemu.site/api/v1/Transacao/SolicitacaoQRCode'
    ]

    for (const url of urlsParaTestar) {
      try {
        console.log(`🔍 Testando URL: ${url}`)
        
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 5000) // 5 segundos timeout

        const startTime = Date.now()
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${PIX_API_TOKEN}`,
            'token': PIX_API_TOKEN,
            'Content-Type': 'application/json'
          },
          signal: controller.signal
        })

        clearTimeout(timeoutId)
        const responseTime = Date.now() - startTime

        const status = {
          url,
          status_code: response.status,
          status_text: response.statusText,
          response_time_ms: responseTime,
          headers: Object.fromEntries(response.headers.entries()),
          accessible: response.status !== 502
        }

        // Tentar ler o corpo da resposta (limitado)
        try {
          const text = await response.text()
          status.response_preview = text.substring(0, 200)
        } catch (e) {
          status.response_preview = 'Erro ao ler resposta'
        }

        diagnostico.urls_testadas.push(status)
        diagnostico.status_apis[url] = status.accessible ? 'OK' : 'ERRO'

      } catch (error) {
        const errorInfo = {
          url,
          erro: error instanceof Error ? error.message : String(error),
          tipo_erro: error instanceof Error ? error.name : 'UNKNOWN',
          accessible: false
        }

        diagnostico.urls_testadas.push(errorInfo)
        diagnostico.status_apis[url] = 'ERRO'
      }

      // Delay entre testes
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    // Análise e recomendações
    const analise = {
      problemas_detectados: [],
      recomendacoes: [],
      status_geral: 'OK'
    }

    // Verificar se há muitos erros 502
    const erros502 = diagnostico.urls_testadas.filter(t => t.status_code === 502).length
    if (erros502 > 0) {
      analise.problemas_detectados.push(`${erros502} URLs retornando erro 502 (Bad Gateway)`)
      analise.recomendacoes.push('Verificar se o servidor da API PIX está funcionando')
      analise.recomendacoes.push('Considerar usar URL alternativa da API')
      analise.status_geral = 'PROBLEMA'
    }

    // Verificar configuração
    if (!process.env.PIX_API_TOKEN) {
      analise.problemas_detectados.push('PIX_API_TOKEN não configurado')
      analise.recomendacoes.push('Configurar PIX_API_TOKEN no arquivo .env.local')
    }

    if (!process.env.PIX_API_URL) {
      analise.problemas_detectados.push('PIX_API_URL não configurado')
      analise.recomendacoes.push('Configurar PIX_API_URL no arquivo .env.local')
    }

    // Verificar inconsistências de URL
    const urlEnv = process.env.PIX_API_URL
    const urlHardcoded = 'https://ouroemu.site/api/v1/MP/webhookruntransation'
    if (urlEnv && !urlHardcoded.includes(urlEnv.replace('/Transacao', ''))) {
      analise.problemas_detectados.push('Inconsistência entre URLs configuradas')
      analise.recomendacoes.push('Verificar se todas as URLs da API PIX estão alinhadas')
    }

    return NextResponse.json({
      success: true,
      diagnostico,
      analise,
      instrucoes: {
        como_corrigir_502: [
          '1. Verificar se o servidor ouroemu.site está online',
          '2. Testar URLs alternativas da API PIX',
          '3. Verificar configurações de proxy/firewall',
          '4. Contatar suporte da API PIX se problema persistir'
        ],
        configuracao_recomendada: {
          'PIX_API_URL': 'https://api.meiodepagamento.com/api/V1',
          'PIX_WEBHOOK_URL': 'https://ouroemu.site/api/v1/MP/webhookruntransation',
          'PIX_API_TOKEN': 'seu_token_aqui'
        }
      }
    })

  } catch (error) {
    console.error('❌ Erro no diagnóstico PIX:', error)
    return NextResponse.json({
      error: 'Erro ao executar diagnóstico',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
