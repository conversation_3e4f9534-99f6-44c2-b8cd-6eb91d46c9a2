import { executeQuery, initializeDatabase } from './lib/database-config.js'

async function testBilheteCreation() {
  try {
    console.log('🧪 Testando criação de bilhete com usuário ID 22...')
    
    await initializeDatabase()
    
    // Dados do bilhete de teste (simulando o que vem do frontend)
    const bilheteData = {
      user_id: 22,
      apostas: [
        { match_id: 535138, resultado: "casa" },
        { match_id: 537642, resultado: "casa" },
        { match_id: 535139, resultado: "empate" },
        { match_id: 537643, resultado: "empate" },
        { match_id: 537644, resultado: "casa" },
        { match_id: 537645, resultado: "casa" },
        { match_id: 537646, resultado: "empate" },
        { match_id: 537647, resultado: "casa" },
        { match_id: 537648, resultado: "casa" },
        { match_id: 537649, resultado: "empate" },
        { match_id: 537650, resultado: "casa" }
      ],
      valor: 0.6,
      qr_code_pix: "00020126810014br.gov.bcb.pix2559qr-code.picpay.com/pix/8263e0f4-9614-42ed-950b-99e5f22de0c05204000053039865802BR5916DLM TECNOLOGIA E6009Sao Paulo62070503***630459EA",
      transaction_id: "pixi_01k0g3g4vrewzr8gn8hd9mgs0t",
      client_name: "João Silva",
      client_email: "<EMAIL>",
      client_document: "123.456.789-00",
      cambista_id: 22
    }
    
    console.log('📤 Dados do bilhete:', {
      user_id: bilheteData.user_id,
      apostas_count: bilheteData.apostas.length,
      valor: bilheteData.valor,
      client_name: bilheteData.client_name,
      client_email: bilheteData.client_email,
      transaction_id: bilheteData.transaction_id
    })
    
    // Verificar se o usuário existe
    const usuario = await executeQuery(`
      SELECT id, nome, email FROM usuarios WHERE id = ?
    `, [bilheteData.user_id])
    
    if (usuario.length === 0) {
      console.log('❌ Usuário não encontrado!')
      return
    }
    
    console.log('✅ Usuário encontrado:', usuario[0])
    
    // Gerar código do bilhete
    const timestamp = Date.now()
    const randomSuffix = Math.random().toString(36).substring(2, 8).toUpperCase()
    const codigoBilhete = `BLT${timestamp}${bilheteData.user_id}${randomSuffix}`
    
    console.log('🔢 Código do bilhete gerado:', codigoBilhete)
    
    // Inserir bilhete
    const result = await executeQuery(`
      INSERT INTO bilhetes (
        codigo, usuario_id, cambista_id, usuario_nome, usuario_email, usuario_cpf,
        valor_total, quantidade_apostas, status, qr_code_pix, transaction_id
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pendente', ?, ?)
    `, [
      codigoBilhete,
      bilheteData.user_id,
      bilheteData.cambista_id || null,
      bilheteData.client_name,
      bilheteData.client_email,
      bilheteData.client_document,
      bilheteData.valor,
      bilheteData.apostas.length,
      bilheteData.qr_code_pix,
      bilheteData.transaction_id
    ])
    
    console.log(`✅ Bilhete criado com ID: ${result.insertId}`)
    
    // Verificar se foi criado
    const bilheteCriado = await executeQuery(`
      SELECT * FROM bilhetes WHERE id = ?
    `, [result.insertId])
    
    if (bilheteCriado.length > 0) {
      const bilhete = bilheteCriado[0]
      console.log('✅ Bilhete confirmado:', {
        id: bilhete.id,
        codigo: bilhete.codigo,
        usuario_nome: bilhete.usuario_nome,
        valor_total: bilhete.valor_total,
        status: bilhete.status,
        transaction_id: bilhete.transaction_id
      })
      
      console.log('\n🧪 Para testar o webhook, use:')
      console.log(`📤 Comando PowerShell:`)
      console.log(`Invoke-RestMethod -Uri "http://localhost:3000/api/v1/MP/webhookruntransation" -Method POST -ContentType "application/json" -Body '{"order_id": "${bilhete.codigo}", "status": "PAID", "type": "PIXOUT", "message": "Payment approved"}'`)
    }
    
  } catch (error) {
    console.error('❌ Erro ao testar criação de bilhete:', error)
  }
}

testBilheteCreation()
