import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // URLs base do sistema
    const baseUrls = {
      development: 'http://localhost:3000',
      production: 'https://ouroemu.site', // ✅ CONFIGURADO
      staging: 'https://staging.ouroemu.site'
    }

    // Obter URL base baseada no ambiente
    const env = process.env.NODE_ENV || 'development'
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || baseUrls[env] || baseUrls.development

    // URLs dos webhooks
    const webhookUrls = {
      main: process.env.PIX_WEBHOOK_URL || `${baseUrl}/api/v1/MP/webhookruntransation`,
      alternative: `${baseUrl}/api/webhook/pix`,
      generic: `${baseUrl}/api/pix/webhook`,
      status: `${baseUrl}/api/pix/status`,
      test: `${baseUrl}/api/test-webhook`,
      debug: `${baseUrl}/api/debug-bilhete`
    }

    // Configurações atuais
    const config = {
      environment: env,
      base_url: baseUrl,
      webhook_urls: webhookUrls,
      pix_api: {
        url: process.env.PIX_API_URL || 'https://ouroemu.site/api/v1',
        token_configured: !!process.env.PIX_API_TOKEN,
        webhook_secret_configured: !!process.env.PIX_WEBHOOK_SECRET
      }
    }

    // Instruções para configuração
    const instructions = {
      title: '🔧 COMO CONFIGURAR O WEBHOOK',
      current_status: {
        webhook_url: webhookUrls.main,
        is_localhost: webhookUrls.main.includes('localhost'),
        needs_configuration: webhookUrls.main.includes('seudominio.com')
      },
      steps: [
        {
          step: 1,
          title: 'Configurar domínio no .env.local',
          description: 'Altere PIX_WEBHOOK_URL para sua URL real',
          example: 'PIX_WEBHOOK_URL=https://meusite.com/api/v1/MP/webhookruntransation'
        },
        {
          step: 2,
          title: 'Configurar na API meiodepagamento.com',
          description: 'Configure o webhook na plataforma PIX',
          webhook_url: webhookUrls.main
        },
        {
          step: 3,
          title: 'Testar webhook',
          description: 'Use o endpoint de teste para verificar',
          test_url: webhookUrls.test
        },
        {
          step: 4,
          title: 'Monitorar bilhetes',
          description: 'Use o debug para verificar status',
          debug_url: webhookUrls.debug
        }
      ],
      env_variables: {
        required: [
          'PIX_WEBHOOK_URL=https://ouroemu.site/api/v1/MP/webhookruntransation',
          'NEXT_PUBLIC_APP_URL=https://ouroemu.site',
          'PIX_API_TOKEN=seu_token_aqui'
        ],
        optional: [
          'PIX_WEBHOOK_SECRET=seu_secret_aqui'
        ]
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Configuração de webhooks',
      config,
      instructions,
      endpoints: {
        'Webhook Principal': 'POST ' + webhookUrls.main,
        'Webhook Alternativo': 'POST ' + webhookUrls.alternative,
        'Webhook Genérico': 'POST ' + webhookUrls.generic,
        'Teste de Webhook': 'POST ' + webhookUrls.test,
        'Debug de Bilhetes': 'GET ' + webhookUrls.debug,
        'Esta Configuração': 'GET ' + `${baseUrl}/api/webhook-config`
      }
    })

  } catch (error) {
    console.error('❌ Erro ao obter configuração de webhook:', error)
    return NextResponse.json({
      error: 'Erro interno do servidor',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { new_webhook_url } = body

    if (!new_webhook_url) {
      return NextResponse.json({
        error: 'new_webhook_url é obrigatório'
      }, { status: 400 })
    }

    // Validar URL
    try {
      new URL(new_webhook_url)
    } catch {
      return NextResponse.json({
        error: 'URL inválida'
      }, { status: 400 })
    }

    return NextResponse.json({
      message: 'Para alterar a URL do webhook, edite o arquivo .env.local',
      current_url: process.env.PIX_WEBHOOK_URL || 'Não configurado',
      new_url_suggested: new_webhook_url,
      instructions: [
        '1. Abra o arquivo .env.local',
        '2. Altere ou adicione: PIX_WEBHOOK_URL=' + new_webhook_url,
        '3. Reinicie o servidor',
        '4. Configure na API meiodepagamento.com'
      ]
    })

  } catch (error) {
    return NextResponse.json({
      error: 'Erro interno do servidor',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
