import { executeQuery, initializeDatabase } from './lib/database-config.js'

async function checkBilhetes() {
  try {
    console.log('🔍 Verificando bilhetes no banco...')
    
    await initializeDatabase()
    
    // Buscar bilhetes mais recentes
    const bilhetes = await executeQuery(`
      SELECT 
        id, 
        codigo, 
        transaction_id, 
        status, 
        valor_total, 
        usuario_nome,
        created_at
      FROM bilhetes 
      ORDER BY created_at DESC 
      LIMIT 10
    `)
    
    console.log(`\n📊 Total de bilhetes encontrados: ${bilhetes.length}`)
    
    if (bilhetes.length > 0) {
      console.log('\n📋 Últimos bilhetes:')
      bilhetes.forEach((bilhete, index) => {
        console.log(`${index + 1}. Código: ${bilhete.codigo}`)
        console.log(`   Transaction ID: ${bilhete.transaction_id || 'N/A'}`)
        console.log(`   Status: ${bilhete.status}`)
        console.log(`   Valor: R$ ${bilhete.valor_total}`)
        console.log(`   Usuário: ${bilhete.usuario_nome}`)
        console.log(`   Criado em: ${bilhete.created_at}`)
        console.log('   ---')
      })
      
      // Buscar bilhetes pendentes
      const bilhetesPendentes = await executeQuery(`
        SELECT 
          codigo, 
          transaction_id, 
          valor_total,
          created_at
        FROM bilhetes 
        WHERE status = 'pendente'
        ORDER BY created_at DESC 
        LIMIT 5
      `)
      
      console.log(`\n⏳ Bilhetes pendentes: ${bilhetesPendentes.length}`)
      bilhetesPendentes.forEach((bilhete, index) => {
        console.log(`${index + 1}. ${bilhete.codigo} - R$ ${bilhete.valor_total} - ${bilhete.created_at}`)
      })
      
    } else {
      console.log('❌ Nenhum bilhete encontrado no banco')
    }
    
  } catch (error) {
    console.error('❌ Erro ao verificar bilhetes:', error)
  }
}

checkBilhetes()
