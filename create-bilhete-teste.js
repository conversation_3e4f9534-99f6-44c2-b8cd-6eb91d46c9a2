import { executeQuery, initializeDatabase } from './lib/database-config.js'

async function createBilheteTeste() {
  try {
    console.log('🎫 Criando bilhete de teste...')
    
    await initializeDatabase()
    
    // Dados do bilhete de teste
    const codigoBilhete = `BLT${Date.now()}23TEST`
    const transactionId = `pixi_test_${Date.now()}`
    
    // Criar bilhete de teste
    const result = await executeQuery(`
      INSERT INTO bilhetes (
        codigo, usuario_id, usuario_nome, usuario_email, usuario_cpf,
        valor_total, quantidade_apostas, status, qr_code_pix, transaction_id
      ) VALUES (?, ?, ?, ?, ?, ?, ?, 'pendente', ?, ?)
    `, [
      codigoBilhete,
      23, // ID do usuário <PERSON> que acabamos de criar
      '<PERSON>',
      '<EMAIL>',
      '12345678901',
      0.60, // Valor de R$ 0,60
      11, // 11 apostas
      '00020126810014br.gov.bcb.pix2559qr-code.picpay.com...',
      transactionId
    ])
    
    console.log(`✅ Bilhete criado com ID: ${result.insertId}`)
    console.log(`📋 Código do bilhete: ${codigoBilhete}`)
    console.log(`🔗 Transaction ID: ${transactionId}`)
    
    // Verificar se foi criado
    const bilheteCriado = await executeQuery(`
      SELECT * FROM bilhetes WHERE id = ?
    `, [result.insertId])
    
    if (bilheteCriado.length > 0) {
      const bilhete = bilheteCriado[0]
      console.log('✅ Bilhete confirmado:', {
        id: bilhete.id,
        codigo: bilhete.codigo,
        usuario_nome: bilhete.usuario_nome,
        valor_total: bilhete.valor_total,
        status: bilhete.status,
        transaction_id: bilhete.transaction_id
      })
      
      console.log('\n🧪 Para testar o webhook, use:')
      console.log(`Código do bilhete: ${bilhete.codigo}`)
      console.log(`Transaction ID: ${bilhete.transaction_id}`)
      
      console.log('\n📤 Comando PowerShell para testar:')
      console.log(`Invoke-RestMethod -Uri "http://localhost:3000/api/v1/MP/webhookruntransation" -Method POST -ContentType "application/json" -Body '{"order_id": "${bilhete.codigo}", "status": "PAID", "type": "PIXOUT", "message": "Payment approved"}'`)
    }
    
  } catch (error) {
    console.error('❌ Erro ao criar bilhete:', error)
  }
}

createBilheteTeste()
