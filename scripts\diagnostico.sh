#!/bin/bash

echo "🔍 DIAGNÓSTICO DO SISTEMA BOLÃO"
echo "================================"

# Informações básicas
echo "📍 Diretório atual: $(pwd)"
echo "👤 Usuário: $(whoami)"
echo "🖥️  Sistema: $(uname -a)"

# Node.js e NPM
echo ""
echo "📦 VERSÕES:"
echo "Node.js: $(node --version 2>/dev/null || echo 'NÃO INSTALADO')"
echo "NPM: $(npm --version 2>/dev/null || echo 'NÃO INSTALADO')"

# Verificar PM2
if command -v pm2 &> /dev/null; then
    echo "PM2: $(pm2 --version)"
else
    echo "PM2: NÃO INSTALADO"
fi

# Verificar arquivos importantes
echo ""
echo "📁 ARQUIVOS:"
echo "package.json: $([ -f package.json ] && echo 'EXISTE' || echo 'NÃO EXISTE')"
echo "next.config.js: $([ -f next.config.js ] && echo 'EXISTE' || echo 'NÃO EXISTE')"
echo "Build (.next): $([ -d .next ] && echo 'EXISTE' || echo 'NÃO EXISTE')"
echo "node_modules: $([ -d node_modules ] && echo 'EXISTE' || echo 'NÃO EXISTE')"

# Verificar binário do Next.js
echo ""
echo "🔧 BINÁRIOS:"
if [ -f "node_modules/.bin/next" ]; then
    echo "Next.js binary: EXISTE"
    echo "Permissões: $(ls -la node_modules/.bin/next | awk '{print $1}')"
    echo "Executável: $([ -x node_modules/.bin/next ] && echo 'SIM' || echo 'NÃO')"
else
    echo "Next.js binary: NÃO EXISTE"
fi

# Verificar portas
echo ""
echo "🌐 PORTAS:"
PORT_3000=$(lsof -ti:3000 2>/dev/null)
PORT_8080=$(lsof -ti:8080 2>/dev/null)

if [ -z "$PORT_3000" ]; then
    echo "Porta 3000: LIVRE"
else
    echo "Porta 3000: EM USO (PID: $PORT_3000)"
fi

if [ -z "$PORT_8080" ]; then
    echo "Porta 8080: LIVRE"
else
    echo "Porta 8080: EM USO (PID: $PORT_8080)"
fi

# Verificar processos Node.js
echo ""
echo "⚡ PROCESSOS NODE.JS:"
NODE_PROCESSES=$(pgrep -f node)
if [ -z "$NODE_PROCESSES" ]; then
    echo "Nenhum processo Node.js rodando"
else
    echo "Processos Node.js encontrados:"
    ps aux | grep node | grep -v grep
fi

# Verificar PM2
echo ""
echo "🔄 PM2 STATUS:"
if command -v pm2 &> /dev/null; then
    pm2 status 2>/dev/null || echo "PM2 sem processos"
else
    echo "PM2 não instalado"
fi

# Verificar espaço em disco
echo ""
echo "💾 ESPAÇO EM DISCO:"
df -h . | tail -1

# Verificar memória
echo ""
echo "🧠 MEMÓRIA:"
free -h | head -2

echo ""
echo "================================"
echo "✅ DIAGNÓSTICO CONCLUÍDO"

# Sugestões baseadas no diagnóstico
echo ""
echo "💡 SUGESTÕES:"

if [ ! -f "node_modules/.bin/next" ]; then
    echo "❌ Next.js não encontrado. Execute: npm install"
fi

if [ ! -x "node_modules/.bin/next" ]; then
    echo "❌ Next.js sem permissão. Execute: chmod -R u+x node_modules/.bin/"
fi

if [ ! -d ".next" ]; then
    echo "❌ Build não encontrado. Execute: npm run build"
fi

if [ ! -z "$PORT_3000" ]; then
    echo "⚠️  Porta 3000 ocupada. Use: PORT=8080 npm start"
fi

if ! command -v pm2 &> /dev/null; then
    echo "💡 Para produção, instale PM2: npm install -g pm2"
fi

echo ""
echo "🚀 COMANDO RÁPIDO PARA INICIAR:"
if [ -z "$PORT_3000" ]; then
    echo "npm start"
else
    echo "PORT=8080 npm start"
fi
