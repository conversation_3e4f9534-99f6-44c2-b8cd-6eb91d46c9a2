# Integração PIX - Sistema Bolão

## Configuração

### Variáveis de Am<PERSON>nte (.env.local)

```env
# PIX Payment API Configuration (ouroemu.site)
PIX_API_URL=https://ouroemu.site/api/v1/Transacao
PIX_API_TOKEN=Gk0e0W5bc2Guozw97NXv8duZbZCDbsedwMuTQNqqn5ZMT549Z0qDDZBF5ZbG9sHhXw9egbly3eiFJ4PKfs6ur+zzXs30gSDV6CYl8sQmSPJaISqpqf/FtJ2k30O3kO9ZsNwasalD0PGpD2wlAbb4ynO5S07P1kgWZRtw4w==
PIX_WEBHOOK_SECRET=your_webhook_secret_here
```

## Endpoints da API

### 1. Gerar QR Code PIX

**POST** `/api/pix/qrcode`

```json
{
  "value": 25.50,
  "description": "Bolão Brasil - 10 apostas",
  "client_name": "<PERSON>",
  "client_email": "<EMAIL>",
  "client_document": "12345678901",
  "qrcode_image": true
}
```

**Resposta de Sucesso:**
```json
{
  "success": true,
  "data": {
    "qr_code_value": "00020126580014br.gov.bcb.pix...",
    "qrcode_image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "expiration_datetime": "2025-07-09T19:30:00Z",
    "status": "PENDING",
    "transaction_id": "pixi_01jznpn5aafpdvn29w7vb4sje6",
    "order_id": "8f5bd8a5869e465ca97574a2d2fa25e4",
    "value": 25.50,
    "client_name": "João Silva",
    "client_email": "<EMAIL>"
  }
}
```

### 2. Consultar Status do Pagamento

**GET** `/api/pix/status?order_id={order_id}`

**Resposta:**
```json
{
  "success": true,
  "order_id": "8f5bd8a5869e465ca97574a2d2fa25e4",
  "status": "PAID", // PENDING, PAID, CANCELLED, FAILED
  "data": {
    // dados adicionais da transação
  }
}
```

### 3. Webhook PIX

**POST** `/api/webhook/pix`

Recebe notificações automáticas da API meiodepagamento.com quando o status do pagamento muda.

```json
{
  "qr_code_payment_id": "qr123456",
  "transaction_id": "txn789012",
  "order_id": "order345678",
  "amount": "25.50",
  "description": "Bolão Brasil - 10 apostas",
  "status": "PAID",
  "end_to_end_id": "E12345678901234567890123456789012",
  "last_updated_at": "2025-07-08T19:00:00Z",
  "error": ""
}
```

## Status dos Pagamentos

- **PENDING**: Aguardando pagamento
- **PAID**: Pagamento confirmado
- **CANCELLED**: Pagamento cancelado
- **FAILED**: Pagamento falhou

## Fluxo de Pagamento

1. **Usuário faz apostas** → Sistema calcula valor total
2. **Gerar QR Code** → Chamada para `/api/pix/qrcode`
3. **Exibir QR Code** → Usuário escaneia e paga
4. **Webhook automático** → API notifica quando pago
5. **Atualizar bilhete** → Status muda para "pago"

## Testes

### Teste da API PIX
```bash
node scripts/test-pix-api.js
```

### Teste da Integração
```bash
node scripts/test-pix-integration.js
```

## Arquivos Importantes

- `lib/pix-api.js` - Biblioteca principal da API PIX
- `app/api/pix/qrcode/route.ts` - Endpoint para gerar QR Code
- `app/api/pix/status/route.ts` - Endpoint para consultar status
- `app/api/webhook/pix/route.ts` - Webhook para receber notificações
- `scripts/test-pix-api.js` - Script de teste da API
- `scripts/test-pix-integration.js` - Script de teste da integração

## Fallback

Se a API PIX estiver indisponível, o sistema usa um fallback que gera:
- QR Code simulado
- Transaction ID único
- Status "pending"

## Logs

O sistema registra todas as operações PIX:
- ✅ Sucesso
- ❌ Erros
- 🔄 Processamento
- 📤 Envios
- 📥 Recebimentos

## Troubleshooting - QR Code "não existe"

### Problema Identificado
O QR Code PIX está sendo gerado corretamente pela API, mas pode não funcionar em alguns aplicativos de banco devido a:

1. **Formato específico da API meiodepagamento.com**: Usa PicPay como intermediário
2. **Validação rigorosa dos bancos**: Alguns apps são mais restritivos
3. **Configuração da conta**: Pode precisar de ativação de recursos

### Soluções Implementadas

#### 1. Validação de Formato
```javascript
// Validações automáticas no QR Code
- Inicia com 00020126 ✅
- Contém br.gov.bcb.pix ✅
- Contém código de moeda (5303986) ✅
- Contém código do país (5802BR) ✅
- Tamanho adequado (100-512 chars) ✅
```

#### 2. Logs Detalhados
```javascript
console.log('🔍 Validando formato PIX:', {
  length: pixResponse.qr_code_value.length,
  startsCorrect: pixResponse.qr_code_value.startsWith('00020126'),
  containsPix: pixResponse.qr_code_value.includes('br.gov.bcb.pix'),
  containsCurrency: pixResponse.qr_code_value.includes('5303986'),
  containsCountry: pixResponse.qr_code_value.includes('5802BR')
})
```

#### 3. Testes Automatizados
- `scripts/test-pix-api.js` - Testa API direta
- `scripts/test-pix-integration.js` - Testa integração completa
- `scripts/validate-pix-format.js` - Valida formato do QR Code
- `scripts/test-pix-real-payment.js` - Teste com pagamento real

### Como Testar se o QR Code Funciona

1. **Copie o código PIX** gerado pelo sistema
2. **Abra seu app do banco** (Nubank, Inter, Bradesco, etc.)
3. **Vá em PIX > Pagar > Colar código**
4. **Cole o código** e verifique se aparece os dados corretos
5. **Se não funcionar**, teste com outro banco

### Códigos PIX de Exemplo Funcionais

```
00020126810014br.gov.bcb.pix2559qr-code.picpay.com/pix/eef70bcb-e3ed-437c-ac43-db9618158afc5204000053039865802BR5916DLM TECNOLOGIA E6009Sao Paulo62070503***6304B675
```

### Status dos Testes Realizados

✅ **API PIX funcionando** - Token válido, resposta 200
✅ **QR Code sendo gerado** - Formato básico correto
✅ **Integração da aplicação** - Endpoints respondendo
✅ **Webhook configurado** - Recebe notificações
⚠️ **Compatibilidade bancos** - Pode variar por instituição

## Segurança

- Token PIX configurado via variáveis de ambiente
- Webhook secret para validar notificações
- Validação de dados obrigatórios
- Logs detalhados para auditoria
