// Testar as funções de cambistas diretamente
import { getCambistas, getCambistasStats, initializeDatabase } from '../lib/database.js'

async function testCambistasAPI() {
  try {
    console.log('🔗 Inicializando banco de dados...')
    await initializeDatabase()
    console.log('✅ Banco inicializado')

    console.log('\n📋 Testando getCambistas...')
    const cambistas = await getCambistas()
    console.log('Cambistas encontrados:', cambistas.length)
    cambistas.forEach((c, i) => {
      console.log(`  ${i + 1}. ${c.nome} (${c.email}) - Status: ${c.status}`)
    })

    console.log('\n📊 Testando getCambistasStats...')
    const stats = await getCambistasStats()
    console.log('Estatísticas:', stats)

  } catch (error) {
    console.error('❌ Erro no teste:', error)
  }
}

testCambistasAPI()
