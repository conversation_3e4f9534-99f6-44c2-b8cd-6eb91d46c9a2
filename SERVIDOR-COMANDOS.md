# 🚀 Comandos para Servidor de Produção

## 📋 Situação Atual
- **Erro**: Porta 3000 já está em uso
- **Localização**: `/www/wwwroot/ouroemu`
- **Problema**: Permission denied nos binários do Node.js

## 🔧 Soluções Rápidas

### Opção 1: Usar Porta 8080 (<PERSON><PERSON>)
```bash
# Navegar para o diretório
cd /www/wwwroot/ouroemu

# Dar permissões aos binários
chmod -R u+x node_modules/.bin/

# Iniciar na porta 8080
PORT=8080 npm start
```

### Opção 2: Liberar Porta 3000
```bash
# Encontrar processo na porta 3000
lsof -ti:3000

# Matar processo (substitua PID pelo número retornado)
kill -9 PID

# Ou matar todos os processos Node.js
pkill -f node

# Depois iniciar normalmente
npm start
```

### Opção 3: Usar PM2 (Recomendado para Produção)
```bash
# Instalar PM2 globalmente
npm install -g pm2

# Dar permissões
chmod -R u+x node_modules/.bin/

# Parar processos anteriores
pm2 stop all
pm2 delete all

# Iniciar com PM2
pm2 start ecosystem.config.js

# Ou iniciar na porta 8080
pm2 start ecosystem.config.js --env port8080
```

## 📊 Comandos de Monitoramento

### Verificar Portas em Uso
```bash
# Ver todas as portas em uso
netstat -tulpn | grep LISTEN

# Ver especificamente porta 3000
lsof -i:3000

# Ver especificamente porta 8080
lsof -i:8080
```

### Monitorar Aplicação
```bash
# Status do PM2
pm2 status

# Logs em tempo real
pm2 logs sistema-bolao-prod

# Monitoramento visual
pm2 monit

# Reiniciar aplicação
pm2 restart sistema-bolao-prod
```

## 🔍 Diagnóstico de Problemas

### Verificar Node.js
```bash
# Versão do Node.js
node --version

# Versão do NPM
npm --version

# Verificar se Next.js está instalado
ls -la node_modules/.bin/next
```

### Verificar Permissões
```bash
# Ver permissões dos binários
ls -la node_modules/.bin/

# Corrigir permissões se necessário
chmod -R u+x node_modules/.bin/
```

### Verificar Build
```bash
# Ver se build existe
ls -la .next/

# Fazer build se necessário
npm run build
```

## 🌐 URLs de Acesso

Após iniciar o servidor:

- **Porta 3000**: http://localhost:3000
- **Porta 8080**: http://localhost:8080
- **IP Externo**: http://SEU_IP:PORTA

## 🚨 Comandos de Emergência

### Parar Tudo
```bash
# Parar PM2
pm2 stop all
pm2 delete all

# Matar todos os processos Node.js
pkill -f node

# Matar processos nas portas específicas
lsof -ti:3000 | xargs kill -9
lsof -ti:8080 | xargs kill -9
```

### Reiniciar Completamente
```bash
# Limpar cache do NPM
npm cache clean --force

# Reinstalar dependências
rm -rf node_modules package-lock.json
npm install

# Fazer build
npm run build

# Iniciar
npm start
```

## ✅ Sequência Recomendada

1. **Primeiro, tente a opção mais simples**:
   ```bash
   cd /www/wwwroot/ouroemu
   chmod -R u+x node_modules/.bin/
   PORT=8080 npm start
   ```

2. **Se funcionar, acesse**: http://localhost:8080

3. **Para produção, use PM2**:
   ```bash
   npm install -g pm2
   pm2 start ecosystem.config.js --env port8080
   ```

4. **Configure proxy reverso** (Nginx/Apache) para redirecionar porta 80 para 8080

## 📞 Suporte

Se ainda houver problemas, execute:
```bash
# Coletar informações do sistema
echo "=== DIAGNÓSTICO ===" > diagnostico.txt
echo "Node.js: $(node --version)" >> diagnostico.txt
echo "NPM: $(npm --version)" >> diagnostico.txt
echo "Diretório: $(pwd)" >> diagnostico.txt
echo "Permissões Next.js: $(ls -la node_modules/.bin/next)" >> diagnostico.txt
echo "Processos na porta 3000: $(lsof -ti:3000)" >> diagnostico.txt
echo "Processos na porta 8080: $(lsof -ti:8080)" >> diagnostico.txt
cat diagnostico.txt
```
