import { executeQuery, initializeDatabase } from './lib/database-config.js'

async function checkUser22() {
  try {
    console.log('🔍 Verificando usuário ID 22...')
    
    await initializeDatabase()
    
    // Buscar usuário ID 22
    const usuario = await executeQuery(`
      SELECT id, nome, email, tipo, status FROM usuarios WHERE id = ?
    `, [22])
    
    if (usuario.length > 0) {
      console.log('✅ Usuário ID 22 encontrado:', usuario[0])
    } else {
      console.log('❌ Usuário ID 22 NÃO encontrado!')
      console.log('🔧 Criando usuário ID 22...')
      
      // Criar usuário ID 22
      const result = await executeQuery(`
        INSERT INTO usuarios (
          id, nome, email, telefone, cpf_cnpj, senha_hash, tipo, status
        ) VALUES (?, ?, ?, ?, ?, ?, 'cambista', 'ativo')
      `, [
        22,
        '<PERSON>',
        '<EMAIL>',
        '(11) 99999-9999',
        '123.456.789-00',
        '$2b$10$teste_hash_cambista'
      ])
      
      console.log(`✅ Usuário ID 22 criado com sucesso!`)
      
      // Verificar se foi criado
      const usuarioCriado = await executeQuery(`
        SELECT id, nome, email, tipo, status FROM usuarios WHERE id = ?
      `, [22])
      
      if (usuarioCriado.length > 0) {
        console.log('✅ Usuário ID 22 confirmado:', usuarioCriado[0])
      }
    }
    
    // Listar todos os usuários para debug
    console.log('\n👥 Todos os usuários no banco:')
    const todosUsuarios = await executeQuery(`
      SELECT id, nome, email, tipo, status FROM usuarios ORDER BY id
    `)
    
    todosUsuarios.forEach((user, index) => {
      console.log(`${index + 1}. ID: ${user.id} - ${user.nome} (${user.email}) - ${user.tipo}`)
    })
    
  } catch (error) {
    console.error('❌ Erro ao verificar usuário:', error)
  }
}

checkUser22()
