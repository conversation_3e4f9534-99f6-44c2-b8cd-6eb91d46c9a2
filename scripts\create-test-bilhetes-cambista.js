// Criar bilhetes de teste para o cambista
import mysql from 'mysql2/promise'

async function createTestBilhetesCambista() {
  let connection
  
  try {
    console.log('🔗 Conectando ao banco de dados...')
    
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'sistema-bolao-top'
    })

    console.log('✅ Conectado ao banco!')

    // Buscar o cambista de teste
    const [cambistas] = await connection.execute(
      "SELECT id, nome, email FROM usuarios WHERE email = '<EMAIL>' AND tipo = 'cambista'"
    )

    if (cambistas.length === 0) {
      console.log('❌ Cambista de teste não encontrado. Execute o script create-test-cambista.js primeiro.')
      return
    }

    const cambista = cambistas[0]
    console.log(`🏪 Cambista encontrado: ${cambista.nome} (ID: ${cambista.id})`)

    // Buscar um usuário para associar aos bilhetes
    const [usuarios] = await connection.execute(
      "SELECT id, nome, email, cpf_cnpj FROM usuarios WHERE tipo = 'usuario' LIMIT 1"
    )

    let usuario
    if (usuarios.length === 0) {
      // Criar um usuário de teste
      console.log('👤 Criando usuário de teste...')
      const [result] = await connection.execute(`
        INSERT INTO usuarios (nome, email, telefone, cpf_cnpj, senha_hash, tipo, status)
        VALUES (?, ?, ?, ?, ?, 'usuario', 'ativo')
      `, [
        'Cliente Teste',
        '<EMAIL>',
        '(11) 99999-8888',
        '987.654.321-00',
        '$2b$10$teste_hash'
      ])
      
      usuario = {
        id: result.insertId,
        nome: 'Cliente Teste',
        email: '<EMAIL>',
        cpf_cnpj: '987.654.321-00'
      }
      console.log(`✅ Usuário criado: ${usuario.nome} (ID: ${usuario.id})`)
    } else {
      usuario = usuarios[0]
      console.log(`👤 Usuário encontrado: ${usuario.nome} (ID: ${usuario.id})`)
    }

    // Criar bilhetes de teste
    const bilhetesParaCriar = [
      {
        valor: 5.00,
        status: 'pago',
        apostas: 3
      },
      {
        valor: 10.00,
        status: 'pago',
        apostas: 5
      },
      {
        valor: 2.50,
        status: 'pendente',
        apostas: 2
      },
      {
        valor: 7.50,
        status: 'pago',
        apostas: 4
      },
      {
        valor: 15.00,
        status: 'pendente',
        apostas: 6
      }
    ]

    console.log('\n🎫 Criando bilhetes de teste...')

    for (let i = 0; i < bilhetesParaCriar.length; i++) {
      const bilhete = bilhetesParaCriar[i]
      const timestamp = Date.now() + i
      const codigo = `BLT${timestamp}${cambista.id}TST`
      
      const [result] = await connection.execute(`
        INSERT INTO bilhetes (
          codigo, usuario_id, cambista_id, usuario_nome, usuario_email, usuario_cpf,
          valor_total, quantidade_apostas, status, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
      `, [
        codigo,
        usuario.id,
        cambista.id,
        usuario.nome,
        usuario.email,
        usuario.cpf_cnpj || '000.000.000-00',
        bilhete.valor,
        bilhete.apostas,
        bilhete.status
      ])
      
      console.log(`  ✅ Bilhete ${i + 1}: ${codigo} - R$ ${bilhete.valor.toFixed(2)} (${bilhete.status})`)
    }

    // Verificar estatísticas
    console.log('\n📊 Verificando estatísticas do cambista...')
    
    const [stats] = await connection.execute(`
      SELECT 
        COUNT(*) as total_bilhetes,
        SUM(valor_total) as valor_total,
        SUM(CASE WHEN status = 'pago' THEN 1 ELSE 0 END) as bilhetes_pagos,
        SUM(CASE WHEN status = 'pendente' THEN 1 ELSE 0 END) as bilhetes_pendentes,
        SUM(CASE WHEN status = 'pago' THEN valor_total ELSE 0 END) as valor_pago
      FROM bilhetes 
      WHERE cambista_id = ?
    `, [cambista.id])

    const estatisticas = stats[0]
    const comissaoTotal = (estatisticas.valor_pago * 10) / 100 // 10% de comissão

    console.log(`📈 Estatísticas do cambista ${cambista.nome}:`)
    console.log(`   Total de bilhetes: ${estatisticas.total_bilhetes}`)
    console.log(`   Bilhetes pagos: ${estatisticas.bilhetes_pagos}`)
    console.log(`   Bilhetes pendentes: ${estatisticas.bilhetes_pendentes}`)
    console.log(`   Valor total vendido: R$ ${parseFloat(estatisticas.valor_total).toFixed(2)}`)
    console.log(`   Valor pago: R$ ${parseFloat(estatisticas.valor_pago).toFixed(2)}`)
    console.log(`   Comissão estimada (10%): R$ ${comissaoTotal.toFixed(2)}`)

    console.log('\n🎯 TESTE DA DASHBOARD:')
    console.log('1. Acesse: http://localhost:3000/cambistas')
    console.log('2. Faça login com:')
    console.log('   Email: <EMAIL>')
    console.log('   Senha: 123456')
    console.log('3. Verifique as estatísticas e bilhetes na dashboard')

  } catch (error) {
    console.error('❌ Erro:', error)
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n🔌 Conexão fechada')
    }
  }
}

createTestBilhetesCambista()
