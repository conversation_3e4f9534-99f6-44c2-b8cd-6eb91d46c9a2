import { executeQuery, initializeDatabase } from './lib/database-config.js'

async function createUser() {
  try {
    console.log('🔧 Criando usuário de teste...')

    await initializeDatabase()

    // Hash simples da senha (para teste)
    const senhaHash = '$2b$10$teste_hash_123456'
    
    // Criar usuário de teste
    const result = await executeQuery(`
      INSERT INTO usuarios (
        nome, email, telefone, cpf_cnpj, senha_hash, tipo, status
      ) VALUES (?, ?, ?, ?, ?, 'usuario', 'ativo')
    `, [
      '<PERSON>',
      '<EMAIL>',
      '11999999999',
      '12345678901',
      senhaHash
    ])
    
    console.log(`✅ Usuário criado com ID: ${result.insertId}`)
    
    // Verificar se foi criado
    const usuarioCriado = await executeQuery(`
      SELECT id, nome, email, tipo, status FROM usuarios WHERE id = ?
    `, [result.insertId])
    
    if (usuarioCriado.length > 0) {
      console.log('✅ Usuário confirmado:', usuarioCriado[0])
      console.log('\n🔑 Credenciais para login:')
      console.log('Email: <EMAIL>')
      console.log('Senha: 123456')
    }
    
  } catch (error) {
    if (error.message.includes('Duplicate entry')) {
      console.log('⚠️ Usuário já existe, buscando dados...')
      
      const usuarioExistente = await executeQuery(`
        SELECT id, nome, email, tipo, status FROM usuarios WHERE email = ?
      `, ['<EMAIL>'])
      
      if (usuarioExistente.length > 0) {
        console.log('✅ Usuário existente:', usuarioExistente[0])
        console.log('\n🔑 Credenciais para login:')
        console.log('Email: <EMAIL>')
        console.log('Senha: 123456')
      }
    } else {
      console.error('❌ Erro ao criar usuário:', error)
    }
  }
}

createUser()
