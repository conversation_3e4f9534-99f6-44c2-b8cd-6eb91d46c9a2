// Testar a API de criação de bilhetes diretamente

const bilheteData = {
  user_id: 22,
  apostas: [
    { match_id: 535138, resultado: "casa" },
    { match_id: 537642, resultado: "casa" },
    { match_id: 535139, resultado: "empate" },
    { match_id: 537643, resultado: "empate" },
    { match_id: 537644, resultado: "casa" },
    { match_id: 537645, resultado: "casa" },
    { match_id: 537646, resultado: "empate" },
    { match_id: 537647, resultado: "casa" },
    { match_id: 537648, resultado: "casa" },
    { match_id: 537649, resultado: "empate" },
    { match_id: 537650, resultado: "casa" }
  ],
  valor: 0.6,
  qr_code_pix: "00020126810014br.gov.bcb.pix2559qr-code.picpay.com/pix/test",
  transaction_id: "pixi_test_api_" + Date.now(),
  client_name: "<PERSON>",
  client_email: "<EMAIL>",
  client_document: "123.456.789-00",
  cambista_id: 22
}

console.log('🧪 Testando API de criação de bilhetes...')
console.log('📤 Dados:', {
  user_id: bilheteData.user_id,
  apostas_count: bilheteData.apostas.length,
  valor: bilheteData.valor,
  client_name: bilheteData.client_name,
  transaction_id: bilheteData.transaction_id
})

fetch('http://localhost:3000/api/bilhetes/create', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(bilheteData)
})
.then(response => {
  console.log('📊 Status da resposta:', response.status)
  return response.json()
})
.then(data => {
  if (data.success) {
    console.log('✅ Bilhete criado com sucesso!')
    console.log('📋 Dados do bilhete:', data.bilhete)
    
    // Testar webhook
    console.log('\n🧪 Testando webhook...')
    const webhookData = {
      order_id: data.bilhete.codigo,
      status: "PAID",
      type: "PIXOUT",
      message: "Payment approved"
    }
    
    return fetch('http://localhost:3000/api/v1/MP/webhookruntransation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(webhookData)
    })
  } else {
    console.error('❌ Erro ao criar bilhete:', data)
    throw new Error(data.error || 'Erro desconhecido')
  }
})
.then(webhookResponse => {
  if (webhookResponse) {
    console.log('📊 Status do webhook:', webhookResponse.status)
    return webhookResponse.json()
  }
})
.then(webhookData => {
  if (webhookData) {
    if (webhookData.processed_successfully) {
      console.log('✅ Webhook processado com sucesso!')
      console.log('📋 Resultado:', {
        bilhete_codigo: webhookData.bilhete_codigo,
        status: webhookData.status,
        valor: webhookData.valor
      })
    } else {
      console.error('❌ Erro no webhook:', webhookData)
    }
  }
})
.catch(error => {
  console.error('❌ Erro no teste:', error.message)
})
