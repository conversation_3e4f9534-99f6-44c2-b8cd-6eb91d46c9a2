import { NextRequest, NextResponse } from "next/server"
import { executeQuery, initializeDatabase } from "@/lib/database-config"

const PIX_API_TOKEN = 'Gk0e0W5bc2Guozw97NXv8duZbZCDbsedwMuTQNqqn5ZMT549Z0qDDZBF5ZbG9sHhXw9egbly3eiFJ4PKfs6ur+zzXs30gSDV6CYl8sQmSPJaISqpqf/FtJ2k30O3kO9ZsNwasalD0PGpD2wlAbb4ynO5S07P1kgWZRtw4w=='

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { transaction_id, order_id, status } = body

    console.log("🔔 Webhook PIX recebido:", { transaction_id, order_id, status })

    // Priorizar order_id se fornecido (webhook)
    const paymentId = order_id || transaction_id

    if (!paymentId) {
      console.log("❌ Webhook: order_id ou transaction_id obrigatório")
      return NextResponse.json({ error: "order_id ou transaction_id é obrigatório" }, { status: 400 })
    }

    // Se status não fornecido, consultar na API interna
    if (!status) {
      console.log("🔍 Consultando status na API interna para:", paymentId)

      try {
        // Usar API interna diretamente
        const response = await fetch(`${PIX_API_BASE_URL}/ConsultarTransacao`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            token: PIX_TOKEN,
            transaction_id: paymentId
          })
        })

        if (!response.ok) {
          console.log("❌ Erro na API interna:", response.status)

          // Se 404, transação não encontrada - não é erro
          if (response.status === 404) {
            return NextResponse.json({
              success: true,
              order_id: paymentId,
              status: "not_found",
              message: "Transação não encontrada na API"
            })
          }

          return NextResponse.json({
            error: "Erro ao consultar status do pagamento",
            order_id: paymentId,
            status: "erro"
          }, { status: 500 })
        }

        const pixResponse = await response.json()
        console.log("📊 Status PIX consultado:", pixResponse)

        return NextResponse.json({
          success: true,
          order_id: paymentId,
          status: pixResponse.status || "pendente",
          data: pixResponse
        })

      } catch (apiError) {
        console.error("❌ Erro na consulta API:", apiError)
        return NextResponse.json({
          error: "Erro ao consultar API de pagamento",
          order_id: paymentId,
          status: "erro"
        }, { status: 500 })
      }
    }

    // Atualizar status no banco de dados
    console.log("💾 Atualizando status no banco:", { paymentId, status })

    try {
      // Mapear status para o banco
      let dbStatus = status
      if (status === 'aprovado' || status === 'approved' || status === 'paid') {
        dbStatus = 'pago'
      }

      // Atualizar bilhete
      const updateResult = await executeQuery(`
        UPDATE bilhetes
        SET status = ?, updated_at = NOW()
        WHERE transaction_id = ? OR codigo = ?
      `, [dbStatus, paymentId, paymentId])

      console.log("✅ Bilhete atualizado:", updateResult)

      // Buscar bilhete atualizado
      const bilhete = await executeQuery(`
        SELECT * FROM bilhetes
        WHERE transaction_id = ? OR codigo = ?
        LIMIT 1
      `, [paymentId, paymentId])

      if (Array.isArray(bilhete) && bilhete.length > 0) {
        console.log("✅ Bilhete encontrado:", bilhete[0])

        return NextResponse.json({
          success: true,
          message: "Status atualizado com sucesso",
          order_id: paymentId,
          status,
          bilhete: bilhete[0]
        })
      } else {
        console.log("⚠️ Bilhete não encontrado para:", paymentId)

        return NextResponse.json({
          success: true,
          message: "Status recebido mas bilhete não encontrado",
          order_id: paymentId,
          status
        })
      }

    } catch (dbError) {
      console.error("❌ Erro ao atualizar banco:", dbError)

      return NextResponse.json({
        success: false,
        error: "Erro ao atualizar status no banco",
        order_id: paymentId,
        status
      }, { status: 500 })
    }

  } catch (error) {
    console.error("❌ Erro geral no webhook:", error)

    return NextResponse.json({
      error: "Erro interno do servidor",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

// Método GET para consultar status e reenviar webhook conforme documentação
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const order_id = searchParams.get('order_id')
  const auto_check = searchParams.get('auto_check')

  // Se auto_check=all, verificar todos os bilhetes pendentes
  if (auto_check === 'all') {
    try {
      console.log("🔄 Iniciando verificação automática de status PIX...")

      await initializeDatabase()

      // Buscar bilhetes pendentes dos últimos 2 dias
      const bilhetesPendentes = await executeQuery(`
        SELECT
          id,
          codigo,
          transaction_id,
          valor_total,
          created_at,
          usuario_id
        FROM bilhetes
        WHERE status = 'pendente'
        AND created_at >= DATE_SUB(NOW(), INTERVAL 2 DAY)
        ORDER BY created_at DESC
        LIMIT 50
      `)

      console.log(`📊 Encontrados ${bilhetesPendentes.length} bilhetes pendentes para verificar`)

      if (bilhetesPendentes.length === 0) {
        return NextResponse.json({
          success: true,
          message: "Nenhum bilhete pendente encontrado",
          checked: 0,
          updated: 0
        })
      }

      let bilhetesAtualizados = 0
      const resultados = []

      // Verificar cada bilhete pendente
      for (const bilhete of bilhetesPendentes) {
        try {
          console.log(`🔍 Verificando bilhete: ${bilhete.codigo}`)

          // Consultar status na API PIX
          const response = await fetch(
            `https://ouroemu.site/api/v1/MP/webhookruntransation?order_id=${bilhete.codigo}`,
            {
              method: 'GET',
              headers: {
                'Authorization': `Bearer ${PIX_API_TOKEN}`,
                'token': PIX_API_TOKEN,
                'Content-Type': 'application/json'
              }
            }
          )

          if (response.ok) {
            const pixData = await response.json()
            console.log(`📊 Status PIX para ${bilhete.codigo}:`, pixData.status)

            // Se o status mudou para PAID, atualizar no banco
            if (pixData.status === 'PAID') {
              await executeQuery(`
                UPDATE bilhetes
                SET status = 'pago', updated_at = NOW()
                WHERE id = ?
              `, [bilhete.id])

              bilhetesAtualizados++

              resultados.push({
                codigo: bilhete.codigo,
                status_anterior: 'pendente',
                status_novo: 'pago',
                valor: parseFloat(bilhete.valor_total)
              })

              console.log(`✅ Bilhete ${bilhete.codigo} atualizado para PAGO`)
            } else {
              resultados.push({
                codigo: bilhete.codigo,
                status_anterior: 'pendente',
                status_novo: 'pendente',
                api_status: pixData.status
              })
            }
          } else if (response.status === 404) {
            console.log(`⚠️ Bilhete ${bilhete.codigo} não encontrado na API PIX`)
            resultados.push({
              codigo: bilhete.codigo,
              status_anterior: 'pendente',
              status_novo: 'pendente',
              api_status: 'NOT_FOUND'
            })
          } else {
            console.log(`❌ Erro ao consultar ${bilhete.codigo}: ${response.status}`)
          }

          // Delay para evitar rate limit
          await new Promise(resolve => setTimeout(resolve, 1000))

        } catch (error) {
          console.error(`❌ Erro ao verificar bilhete ${bilhete.codigo}:`, error)
          resultados.push({
            codigo: bilhete.codigo,
            status_anterior: 'pendente',
            status_novo: 'pendente',
            erro: error instanceof Error ? error.message : String(error)
          })
        }
      }

      console.log(`✅ Verificação concluída: ${bilhetesAtualizados} bilhetes atualizados`)

      return NextResponse.json({
        success: true,
        message: `Verificação automática concluída`,
        checked: bilhetesPendentes.length,
        updated: bilhetesAtualizados,
        resultados: resultados
      })

    } catch (error) {
      console.error("❌ Erro na verificação automática:", error)
      return NextResponse.json({
        error: "Erro na verificação automática",
        details: error instanceof Error ? error.message : String(error)
      }, { status: 500 })
    }
  }

  if (!order_id) {
    return NextResponse.json({
      message: "Endpoint para consultar status PIX e reenviar webhook",
      usage: "GET /api/pix/status?order_id=xxx",
      auto_check: "GET /api/pix/status?auto_check=all (verifica todos os bilhetes pendentes)",
      documentation: {
        description: "Consulta status da transação e reenvia webhook se necessário",
        possible_status: {
          PAID: "Transação paga e webhook reenviado",
          PENDING: "Transação localizada, mas aguardando pagamento",
          NOTFOUND: "Nenhuma transação foi localizada"
        },
        possible_messages: {
          PAID: "Webhook executed successfully",
          PENDING: "awaiting payment",
          NOTFOUND: "Transaction not found"
        }
      }
    })
  }

  console.log("🔍 Consultando status e reenviando webhook para order_id:", order_id)

  try {
    // Consultar status na API PIX conforme documentação
    const response = await fetch(
      `https://ouroemu.site/api/v1/MP/webhookruntransation?order_id=${order_id}`,
      {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${PIX_API_TOKEN}`,
          'token': PIX_API_TOKEN,
          'Content-Type': 'application/json'
        }
      }
    )

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ Erro na API PIX:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText
      })

      if (response.status === 404) {
        return NextResponse.json(
          {
            status: 'NOTFOUND',
            message: 'Transaction not found',
            order_id: order_id
          },
          { status: 404 }
        )
      }

      return NextResponse.json(
        {
          error: 'Erro ao consultar status na API PIX',
          details: errorText
        },
        { status: response.status }
      )
    }

    const pixData = await response.json()

    console.log('✅ Status consultado e webhook reenviado:', {
      order_id: order_id,
      status: pixData.status,
      message: pixData.message
    })

    // Mapear resposta conforme documentação
    let statusResponse = {
      order_id: order_id,
      status: pixData.status || 'UNKNOWN',
      message: pixData.message || 'Status consultado',
      transaction_data: pixData
    }

    // Interpretar diferentes status conforme documentação
    switch (pixData.status) {
      case 'PAID':
        statusResponse.message = 'Webhook executed successfully'
        break
      case 'PENDING':
        statusResponse.message = 'awaiting payment'
        break
      case 'NOTFOUND':
        statusResponse.message = 'Transaction not found'
        break
      default:
        statusResponse.message = pixData.message || 'Status consultado'
    }

    return NextResponse.json(statusResponse)

  } catch (error) {
    console.error("❌ Erro na consulta GET:", error)

    return NextResponse.json({
      error: "Erro interno do servidor",
      message: "Falha ao consultar status da transação",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
