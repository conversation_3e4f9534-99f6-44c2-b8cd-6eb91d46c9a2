const mysql = require('mysql2/promise')

async function testWebhookFix() {
  let connection
  
  try {
    console.log('🔧 Conectando ao MySQL...')
    
    connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      database: 'sistema-bolao-top',
      charset: 'utf8mb4'
    })
    
    console.log('✅ Conectado ao MySQL')
    
    // Buscar o bilhete mais recente que está pendente
    console.log('🔍 Buscando bilhete pendente mais recente...')
    const [bilhetes] = await connection.execute(`
      SELECT id, codigo, transaction_id, pix_order_id, status, created_at
      FROM bilhetes 
      WHERE status = 'pendente'
      ORDER BY created_at DESC 
      LIMIT 1
    `)
    
    if (bilhetes.length === 0) {
      console.log('⚠️ Nenhum bilhete pendente encontrado')
      return
    }
    
    const bilhete = bilhetes[0]
    console.log('📋 Bilhete encontrado:', {
      id: bilhete.id,
      codigo: bilhete.codigo,
      transaction_id: bilhete.transaction_id,
      pix_order_id: bilhete.pix_order_id,
      status: bilhete.status
    })
    
    // Se não tem pix_order_id, vamos simular um
    let orderIdParaTeste = bilhete.pix_order_id
    
    if (!orderIdParaTeste) {
      orderIdParaTeste = `test_order_${Date.now()}`
      console.log(`⚠️ Bilhete sem pix_order_id, simulando: ${orderIdParaTeste}`)
      
      // Atualizar o bilhete com um pix_order_id de teste
      await connection.execute(`
        UPDATE bilhetes 
        SET pix_order_id = ? 
        WHERE id = ?
      `, [orderIdParaTeste, bilhete.id])
      
      console.log('✅ pix_order_id de teste adicionado ao bilhete')
    }
    
    console.log('🚀 Testando webhook com order_id:', orderIdParaTeste)
    
    // Fazer requisição para o webhook
    const webhookData = {
      order_id: orderIdParaTeste,
      status: 'PAID',
      type: 'PIXOUT',
      message: 'Payment approved - TEST'
    }
    
    console.log('📤 Enviando webhook:', webhookData)
    
    const response = await fetch('http://localhost:3000/api/v1/MP/webhookruntransation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(webhookData)
    })
    
    const result = await response.json()
    console.log('📥 Resposta do webhook:', result)
    
    // Verificar se o bilhete foi atualizado
    console.log('🔍 Verificando se bilhete foi atualizado...')
    const [bilheteAtualizado] = await connection.execute(`
      SELECT id, codigo, status, updated_at
      FROM bilhetes 
      WHERE id = ?
    `, [bilhete.id])
    
    if (bilheteAtualizado.length > 0) {
      const bilheteNovo = bilheteAtualizado[0]
      console.log('📊 Status do bilhete após webhook:', {
        id: bilheteNovo.id,
        codigo: bilheteNovo.codigo,
        status_anterior: bilhete.status,
        status_novo: bilheteNovo.status,
        updated_at: bilheteNovo.updated_at
      })
      
      if (bilheteNovo.status === 'pago') {
        console.log('🎉 SUCESSO! Webhook funcionou corretamente!')
      } else {
        console.log('❌ FALHA! Bilhete não foi atualizado para "pago"')
      }
    }
    
  } catch (error) {
    console.error('❌ Erro:', error.message)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('🔌 Conexão MySQL fechada')
    }
  }
}

// Executar
testWebhookFix()
  .then(() => {
    console.log('🎉 Teste concluído!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Teste falhou:', error)
    process.exit(1)
  })
