import { executeQuery, initializeDatabase } from './lib/database-config.js'

async function checkUsuarios() {
  try {
    console.log('🔍 Verificando usuários no banco...')
    
    await initializeDatabase()
    
    // Buscar usuários
    const usuarios = await executeQuery(`
      SELECT
        id,
        nome,
        email,
        tipo,
        status
      FROM usuarios
      ORDER BY id ASC
      LIMIT 10
    `)
    
    console.log(`\n📊 Total de usuários encontrados: ${usuarios.length}`)
    
    if (usuarios.length > 0) {
      console.log('\n👥 Usuários cadastrados:')
      usuarios.forEach((usuario, index) => {
        console.log(`${index + 1}. ID: ${usuario.id}`)
        console.log(`   Nome: ${usuario.nome}`)
        console.log(`   Email: ${usuario.email}`)
        console.log(`   Tipo: ${usuario.tipo}`)
        console.log(`   Status: ${usuario.status}`)
        console.log(`   Status: ${usuario.status}`)
        console.log('   ---')
      })
    } else {
      console.log('❌ Nenhum usuário encontrado no banco')
      console.log('\n🔧 Criando usuário de teste...')
      
      // Criar usuário de teste
      const result = await executeQuery(`
        INSERT INTO usuarios (
          nome, email, telefone, cpf_cnpj, senha_hash, tipo, status
        ) VALUES (?, ?, ?, ?, ?, 'usuario', 'ativo')
      `, [
        'Usuário Teste',
        '<EMAIL>',
        '11999999999',
        '12345678901',
        '$2b$10$teste_hash_senha'
      ])
      
      console.log(`✅ Usuário de teste criado com ID: ${result.insertId}`)
      
      // Verificar se foi criado
      const usuarioCriado = await executeQuery(`
        SELECT id, nome, email FROM usuarios WHERE id = ?
      `, [result.insertId])
      
      if (usuarioCriado.length > 0) {
        console.log('✅ Usuário de teste confirmado:', usuarioCriado[0])
      }
    }
    
  } catch (error) {
    console.error('❌ Erro ao verificar usuários:', error)
  }
}

checkUsuarios()
