#!/usr/bin/env node

/**
 * Script para verificar se todas as configurações de domínio estão corretas
 * Domínio: https://ouroemu.site
 */

import fs from 'fs'
import path from 'path'

const TARGET_DOMAIN = 'https://ouroemu.site'
const OLD_DOMAINS = [
  'https://seudominio.com',
  'https://baixiaki.store',
  'https://rrsistemasbolaodemo.site',
  'http://localhost:3000'
]

console.log('🔍 VERIFICANDO CONFIGURAÇÕES DE DOMÍNIO')
console.log('=' .repeat(50))
console.log(`🎯 Domínio alvo: ${TARGET_DOMAIN}`)
console.log('')

// Arquivos para verificar
const filesToCheck = [
  '.env.local',
  '.env.production',
  'config/webhook.config.js',
  'app/api/webhook-config/route.ts',
  'scripts/deploy-production.js',
  'next.config.mjs'
]

let allCorrect = true
let issues = []

// Verificar cada arquivo
filesToCheck.forEach(filePath => {
  console.log(`📄 Verificando: ${filePath}`)
  
  if (!fs.existsSync(filePath)) {
    console.log(`   ⚠️  Arquivo não encontrado`)
    issues.push(`Arquivo ${filePath} não encontrado`)
    allCorrect = false
    return
  }

  const content = fs.readFileSync(filePath, 'utf8')
  
  // Verificar se contém o domínio correto
  const hasCorrectDomain = content.includes('ouroemu.site')
  
  // Verificar se ainda contém domínios antigos
  const oldDomainsFound = OLD_DOMAINS.filter(domain => 
    content.includes(domain.replace('https://', '').replace('http://', ''))
  )

  if (hasCorrectDomain) {
    console.log(`   ✅ Contém domínio correto`)
  } else {
    console.log(`   ❌ NÃO contém domínio correto`)
    issues.push(`${filePath} não contém ouroemu.site`)
    allCorrect = false
  }

  if (oldDomainsFound.length > 0) {
    console.log(`   ⚠️  Ainda contém domínios antigos: ${oldDomainsFound.join(', ')}`)
    issues.push(`${filePath} ainda contém domínios antigos: ${oldDomainsFound.join(', ')}`)
    allCorrect = false
  }

  console.log('')
})

// Verificar variáveis de ambiente específicas
console.log('🔧 VERIFICANDO VARIÁVEIS DE AMBIENTE')
console.log('=' .repeat(50))

const envFiles = ['.env.local', '.env.production']
envFiles.forEach(envFile => {
  if (fs.existsSync(envFile)) {
    console.log(`📄 ${envFile}:`)
    const content = fs.readFileSync(envFile, 'utf8')
    
    // Variáveis importantes
    const importantVars = [
      'NEXT_PUBLIC_APP_URL',
      'PIX_WEBHOOK_URL',
      'EMAIL_SUPORTE'
    ]

    importantVars.forEach(varName => {
      const regex = new RegExp(`${varName}=(.+)`)
      const match = content.match(regex)
      
      if (match) {
        const value = match[1].trim()
        const isCorrect = value.includes('ouroemu.site')
        console.log(`   ${varName}: ${value} ${isCorrect ? '✅' : '❌'}`)
        
        if (!isCorrect) {
          issues.push(`${envFile}: ${varName} não está configurado para ouroemu.site`)
          allCorrect = false
        }
      } else {
        console.log(`   ${varName}: NÃO ENCONTRADO ❌`)
        issues.push(`${envFile}: ${varName} não encontrado`)
        allCorrect = false
      }
    })
    console.log('')
  }
})

// Resultado final
console.log('📊 RESULTADO DA VERIFICAÇÃO')
console.log('=' .repeat(50))

if (allCorrect) {
  console.log('🎉 TODAS AS CONFIGURAÇÕES ESTÃO CORRETAS!')
  console.log(`✅ Domínio ${TARGET_DOMAIN} configurado em todos os arquivos`)
  console.log('')
  console.log('🚀 PRÓXIMOS PASSOS:')
  console.log('1. Fazer build: npm run build')
  console.log('2. Testar localmente: npm run start')
  console.log('3. Deploy para produção')
  console.log('4. Configurar SSL/HTTPS no servidor')
  console.log('5. Testar webhooks PIX')
} else {
  console.log('❌ PROBLEMAS ENCONTRADOS:')
  issues.forEach((issue, index) => {
    console.log(`${index + 1}. ${issue}`)
  })
  console.log('')
  console.log('🔧 CORREÇÕES NECESSÁRIAS:')
  console.log('- Substitua todos os domínios antigos por https://ouroemu.site')
  console.log('- Verifique as variáveis de ambiente')
  console.log('- Execute este script novamente após as correções')
}

console.log('')
console.log('🌐 URLs importantes após deploy:')
console.log(`- Site principal: ${TARGET_DOMAIN}`)
console.log(`- Painel admin: ${TARGET_DOMAIN}/admin`)
console.log(`- Dashboard cambistas: ${TARGET_DOMAIN}/cambistas`)
console.log(`- Webhook PIX: ${TARGET_DOMAIN}/api/v1/MP/webhookruntransation`)
console.log(`- Configuração webhook: ${TARGET_DOMAIN}/api/webhook-config`)

process.exit(allCorrect ? 0 : 1)
