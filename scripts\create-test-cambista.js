// Criar cambista de teste para login no frontend principal
import mysql from 'mysql2/promise'
import bcrypt from 'bcryptjs'

async function createTestCambista() {
  let connection
  
  try {
    console.log('🔗 Conectando ao banco de dados...')
    
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'sistema-bolao-top'
    })

    console.log('✅ Conectado ao banco!')

    // Verificar se já existe um cambista de teste
    const [existingCambistas] = await connection.execute(
      "SELECT id, nome, email FROM usuarios WHERE tipo = 'cambista'"
    )

    console.log(`📊 Cambistas existentes: ${existingCambistas.length}`)
    
    if (existingCambistas.length > 0) {
      console.log('📋 Cambistas encontrados:')
      existingCambistas.forEach((cambista, index) => {
        console.log(`  ${index + 1}. ${cambista.nome} (${cambista.email}) - ID: ${cambista.id}`)
      })
    }

    // Criar cambista de teste se não existir
    const testEmail = '<EMAIL>'
    const existingTest = existingCambistas.find(c => c.email === testEmail)
    
    if (!existingTest) {
      console.log('\n👤 Criando cambista de teste...')
      
      // Hash da senha "123456"
      const senhaHash = await bcrypt.hash('123456', 10)
      
      const [result] = await connection.execute(`
        INSERT INTO usuarios (nome, email, telefone, endereco, cpf_cnpj, senha_hash, tipo, status, data_cadastro)
        VALUES (?, ?, ?, ?, ?, ?, 'cambista', 'ativo', NOW())
      `, [
        'João Silva',
        '<EMAIL>', 
        '(11) 99999-9999',
        'Rua das Flores, 123',
        '123.456.789-00',
        senhaHash
      ])
      
      console.log(`✅ Cambista criado com ID: ${result.insertId}`)
      console.log('📧 Email: <EMAIL>')
      console.log('🔑 Senha: 123456')
    } else {
      console.log(`\n✅ Cambista de teste já existe: ${existingTest.nome} (ID: ${existingTest.id})`)
      console.log('📧 Email: <EMAIL>')
      console.log('🔑 Senha: 123456')
    }

    console.log('\n🎯 INSTRUÇÕES:')
    console.log('1. Acesse o frontend principal (localhost:3000)')
    console.log('2. Clique em "Entrar na sua conta"')
    console.log('3. Use as credenciais:')
    console.log('   Email: <EMAIL>')
    console.log('   Senha: 123456')
    console.log('4. Quando logado como cambista, a impressão automática será ativada!')

  } catch (error) {
    console.error('❌ Erro:', error)
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n🔌 Conexão fechada')
    }
  }
}

createTestCambista()
