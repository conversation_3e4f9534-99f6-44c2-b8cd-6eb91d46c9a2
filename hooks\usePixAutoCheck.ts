'use client'

import { useEffect, useRef } from 'react'

export function usePixAutoCheck() {
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    // Função para executar a verificação automática
    const executarVerificacaoPixAutomatica = async () => {
      try {
        console.log('🔄 Executando verificação automática de PIX...')

        const response = await fetch('/api/pix/status?auto_check=all', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        })

        if (response.ok) {
          const result = await response.json()
          console.log('✅ Verificação automática PIX concluída:', result)
          
          // Se algum bilhete foi atualizado, mostrar notificação
          if (result.updated > 0) {
            console.log(`🎉 ${result.updated} bilhete(s) atualizado(s) para PAGO!`)
            
            // Disparar evento customizado para atualizar a UI
            window.dispatchEvent(new CustomEvent('pixStatusUpdated', {
              detail: { updated: result.updated, resultados: result.resultados }
            }))
          }
        } else {
          console.error('❌ Erro na verificação automática PIX:', response.status)
        }
      } catch (error) {
        console.error('❌ Erro na verificação automática PIX:', error)
      }
    }

    // Executar verificação imediatamente ao carregar
    executarVerificacaoPixAutomatica()

    // Configurar intervalo de 2 minutos (2 * 60 * 1000 = 120000ms)
    intervalRef.current = setInterval(executarVerificacaoPixAutomatica, 2 * 60 * 1000)

    // Cleanup ao desmontar
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }
  }, [])

  return null
}
