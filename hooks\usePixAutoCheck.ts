'use client'

import { useEffect, useRef } from 'react'

export function usePixAutoCheck() {
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    // Função para executar a verificação automática
    const executarVerificacaoPixAutomatica = async () => {
      try {
        console.log('🔄 Executando verificação automática de PIX...')

        // Verificar bilhetes do usuário atual
        const userId = localStorage.getItem('user_id')
        if (!userId) {
          console.log('⚠️ Usuário não logado, pulando verificação PIX')
          return
        }

        // Buscar bilhetes pendentes do usuário
        const bilhetesResponse = await fetch(`/api/user/bilhetes?user_id=${userId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        })

        if (!bilhetesResponse.ok) {
          console.error('❌ Erro ao buscar bilhetes:', bilhetesResponse.status)
          return
        }

        const bilhetesData = await bilhetesResponse.json()
        const bilhetesPendentes = bilhetesData.bilhetes?.filter((b: any) => b.status === 'pendente') || []

        console.log(`📊 Encontrados ${bilhetesPendentes.length} bilhetes pendentes`)

        // Verificar cada bilhete pendente
        for (const bilhete of bilhetesPendentes) {
          try {
            console.log(`🔍 Verificando pagamento do bilhete: ${bilhete.codigo}`)

            // Usar endpoint específico para verificação de pagamento
            const checkResponse = await fetch('/api/pix/check-payment', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                bilhete_codigo: bilhete.codigo,
                transaction_id: bilhete.transaction_id
              })
            })

            if (checkResponse.ok) {
              const checkResult = await checkResponse.json()

              if (checkResult.payment_confirmed && checkResult.just_confirmed) {
                console.log(`🎉 Bilhete ${bilhete.codigo} foi PAGO!`)

                // Disparar evento customizado para mostrar modal de sucesso
                window.dispatchEvent(new CustomEvent('pixPaymentConfirmed', {
                  detail: {
                    codigo: bilhete.codigo,
                    valor: bilhete.valor,
                    data: new Date().toLocaleString('pt-BR'),
                    transactionId: bilhete.transaction_id || bilhete.codigo
                  }
                }))
              }
            }
          } catch (error) {
            console.error(`❌ Erro ao verificar bilhete ${bilhete.codigo}:`, error)
          }
        }

      } catch (error) {
        console.error('❌ Erro na verificação automática PIX:', error)
      }
    }

    // Executar verificação imediatamente ao carregar
    executarVerificacaoPixAutomatica()

    // Configurar intervalo de 10 segundos para verificação rápida
    // Isso permite detecção quase instantânea de pagamentos
    intervalRef.current = setInterval(executarVerificacaoPixAutomatica, 10 * 1000)

    // Cleanup ao desmontar
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }
  }, [])

  return null
}
